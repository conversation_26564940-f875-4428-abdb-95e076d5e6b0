#!/usr/bin/env python3
import os
import logging
from pathlib import Path
from pypdf import PdfReader, PdfWriter
from PIL import Image
import io
import argparse
import time
import sys
import csv
import urllib.parse

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("watermark_reapply.log")
    ]
)
logger = logging.getLogger("watermark-reapply")

# Print to both console and log file
def log_print(message):
    logger.info(message)
    print(message, flush=True)
    
def count_lines_in_csv(csv_path):
    """
    Count the number of lines in a CSV file, excluding the header.
    
    Args:
        csv_path: Path to the CSV file
        
    Returns:
        Number of lines (excluding header)
    """
    try:
        with open(csv_path, 'r') as csvfile:
            # Count lines but subtract 1 for the header (if it exists)
            line_count = sum(1 for line in csvfile if line.strip())
            return max(0, line_count - 1)  # Ensure we don't return negative numbers
    except Exception as e:
        log_print(f"ERROR: Failed to count lines in CSV: {str(e)}")
        return 0

def read_target_files(csv_path, base_dir):
    """
    Read the CSV file containing target file URLs and convert them to file paths.
    
    Args:
        csv_path: Path to the CSV file containing file URLs
        base_dir: Base directory where files are stored (/app/uploads in container, /singleaudit/uploads on host)
        
    Returns:
        List of file paths to process
    """
    target_files = []
    
    try:
        with open(csv_path, 'r') as csvfile:
            reader = csv.reader(csvfile)
            # Skip header if it exists
            header = next(reader, None)
            
            for row in reader:
                if not row:  # Skip empty rows
                    continue
                    
                # Assume the file URL is in the first column
                file_url = row[0].strip()
                
                # Convert URL to file path
                if file_url.startswith('http'):
                    # Parse the URL and extract the path
                    parsed_url = urllib.parse.urlparse(file_url)
                    file_path = urllib.parse.unquote(parsed_url.path)
                    
                    # Remove any leading path components that might be part of the URL but not the file system
                    # Based on docker-compose.yml, /singleaudit/uploads on host is mounted as /app/uploads in container
                    if file_path.startswith('/app/uploads/'):
                        file_path = file_path.replace('/app/uploads/', '')
                    elif file_path.startswith('/uploads/'):
                        file_path = file_path.replace('/uploads/', '')
                    elif file_path.startswith('./uploads/'):
                        file_path = file_path.replace('./uploads/', '')
                        
                    # Construct the full path
                    full_path = os.path.join(base_dir, file_path)
                else:
                    # If it's already a path, handle various formats
                    if file_url.startswith('/app/uploads/'):
                        file_path = file_url.replace('/app/uploads/', '')
                    elif file_url.startswith('/uploads/'):
                        file_path = file_url.replace('/uploads/', '')
                    elif file_url.startswith('./uploads/'):
                        file_path = file_url.replace('./uploads/', '')
                    else:
                        file_path = file_url
                        
                    # Construct the full path
                    full_path = os.path.join(base_dir, file_path)
                
                target_files.append(full_path)
                
        return target_files
    except Exception as e:
        log_print(f"ERROR: Failed to read target files from CSV: {str(e)}")
        return []

def image_to_pdf(stamp_img_path):
    """Convert an image to a PDF page that can be used as a watermark."""
    img = Image.open(stamp_img_path)
    img_as_pdf = io.BytesIO()
    img.save(img_as_pdf, "pdf")
    img_as_pdf.seek(0)
    return PdfReader(img_as_pdf)

def add_watermark_to_pdf(input_pdf_path, watermark_page, output_pdf_path=None):
    """Add watermark to a PDF file."""
    if output_pdf_path is None:
        output_pdf_path = input_pdf_path
    
    try:
        # Open the PDF
        pdf_writer = PdfWriter()
        with open(input_pdf_path, "rb") as input_file:
            pdf_reader = PdfReader(input_file)
            
            # Iterate through each page
            for page in pdf_reader.pages:
                # Merge watermark with the page
                page.merge_page(watermark_page, over=True)
                pdf_writer.add_page(page)
        
        # Write the output PDF
        with open(output_pdf_path, "wb") as output_file:
            pdf_writer.write(output_file)
        
        return True
    except Exception as e:
        logger.error(f"Error processing {input_pdf_path}: {str(e)}")
        return False

def process_target_files(target_csv, base_dir, watermark_path, dry_run=False, batch_size=100, sleep_time=1):
    """
    Process PDF files listed in the target CSV file and add watermark.
    
    Args:
        target_csv: Path to the CSV file containing target file URLs
        base_dir: The base directory where files are stored
        watermark_path: Path to the watermark image
        dry_run: If True, only log what would be done without making changes
        batch_size: Number of files to process before sleeping
        sleep_time: Time to sleep in seconds between batches
    """
    # Convert watermark image to PDF
    try:
        watermark_pdf = image_to_pdf(watermark_path)
        watermark_page = watermark_pdf.pages[0]
    except Exception as e:
        log_print(f"ERROR: Failed to load watermark image: {str(e)}")
        return
    
    # Count total files in CSV for task summary
    total_csv_lines = count_lines_in_csv(target_csv)
    log_print(f"Task Summary: {total_csv_lines} files to process from CSV file")
    
    # Read target files from CSV
    log_print(f"Reading target files from {target_csv}...")
    pdf_files = read_target_files(target_csv, base_dir)
    
    total_files = len(pdf_files)
    log_print(f"Found {total_files} valid file paths to process in the target list")
    
    # Print the list of files to be processed
    log_print("Files to be processed:")
    for i, pdf_path in enumerate(pdf_files):
        log_print(f"  {i+1}. {pdf_path}")
    
    if total_files == 0:
        log_print("No files found in the target list. Exiting.")
        return
    
    # Process files in batches
    processed_count = 0
    success_count = 0
    error_count = 0
    
    start_time = time.time()
    
    for i, pdf_path in enumerate(pdf_files):
        if i > 0 and i % batch_size == 0:
            elapsed = time.time() - start_time
            estimated_total = (elapsed / i) * total_files
            remaining = estimated_total - elapsed
            
            # Detailed batch report
            progress_msg = f"Progress: {i}/{total_files} files ({i/total_files*100:.1f}%)"
            time_msg = f"Elapsed: {elapsed:.1f}s, Est. remaining: {remaining:.1f}s"
            success_rate = f"Success rate: {success_count}/{processed_count} ({success_count/processed_count*100:.1f}%)"
            error_rate = f"Error rate: {error_count}/{processed_count} ({error_count/processed_count*100:.1f}%)"
            
            log_print("=" * 80)
            log_print(f"BATCH REPORT (after {i} files)")
            log_print(f"  {progress_msg}")
            log_print(f"  {success_rate}")
            log_print(f"  {error_rate}")
            log_print(f"  {time_msg}")
            log_print(f"Sleeping for {sleep_time} seconds before next batch...")
            log_print("=" * 80)
            
            time.sleep(sleep_time)
        
        try:
            pdf_path_str = str(pdf_path)
            
            # Check if file exists
            if not os.path.exists(pdf_path_str):
                log_print(f"WARNING: File not found: {pdf_path_str}")
                error_count += 1
                processed_count += 1
                continue
                
            # Check if it's a PDF file
            if not pdf_path_str.lower().endswith('.pdf'):
                log_print(f"WARNING: Not a PDF file: {pdf_path_str}")
                error_count += 1
                processed_count += 1
                continue
                
            if dry_run:
                if i % 100 == 0 or i < 10:  # Only log every 100th file in dry run to avoid excessive output
                    log_print(f"[DRY RUN] Would process: {pdf_path_str}")
                success_count += 1
            else:
                if i % 100 == 0 or i < 10:  # Only log every 100th file to avoid excessive output
                    log_print(f"Processing: {pdf_path_str}")
                if add_watermark_to_pdf(pdf_path_str, watermark_page):
                    success_count += 1
                else:
                    error_count += 1
        except Exception as e:
            logger.error(f"Error processing {pdf_path}: {str(e)}")
            error_count += 1
        
        processed_count += 1
        
        # Print progress every 5% or at least every 1000 files
        if processed_count % max(int(total_files * 0.05), 1000) == 0 or processed_count == total_files:
            log_print(f"Progress update: {processed_count}/{total_files} files processed ({processed_count/total_files*100:.1f}%)")
    
    total_time = time.time() - start_time
    
    # Calculate processing rate
    processing_rate = processed_count / total_time if total_time > 0 else 0
    
    # Final detailed report
    log_print("=" * 80)
    log_print("FINAL REPORT")
    log_print("=" * 80)
    log_print(f"Total files in CSV: {total_csv_lines}")
    log_print(f"Valid file paths found: {total_files}")
    log_print(f"Files processed: {processed_count}")
    log_print(f"Successful: {success_count} ({success_count/processed_count*100:.1f}% of processed)")
    log_print(f"Errors: {error_count} ({error_count/processed_count*100:.1f}% of processed)")
    log_print(f"Total processing time: {total_time:.1f} seconds")
    log_print(f"Processing rate: {processing_rate:.2f} files/second")
    log_print("=" * 80)

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Reapply watermarks to PDF files")
    parser.add_argument("--base-dir", default="/app/uploads", help="Base directory where files are stored")
    parser.add_argument("--watermark", default="./app/services/watermark.png", help="Path to watermark image")
    parser.add_argument("--target-csv", default="./target/target_files.csv", help="Path to CSV file containing target file URLs")
    parser.add_argument("--dry-run", action="store_true", help="Dry run mode (no changes)")
    parser.add_argument("--batch-size", type=int, default=100, help="Number of files to process before sleeping")
    parser.add_argument("--sleep-time", type=float, default=1.0, help="Time to sleep in seconds between batches")
    
    args = parser.parse_args()
    
    log_print("=" * 80)
    log_print("NHSO SINGLE AUDIT PLATFORM - WATERMARK REAPPLICATION TOOL")
    log_print("=" * 80)
    log_print(f"Starting watermark reapplication process")
    log_print(f"Base directory: {args.base_dir}")
    log_print(f"Target CSV: {args.target_csv}")
    log_print(f"Watermark image: {args.watermark}")
    log_print(f"Dry run: {args.dry_run}")
    log_print(f"Batch size: {args.batch_size}")
    log_print(f"Sleep time: {args.sleep_time} seconds")
    log_print("-" * 80)
    
    # Check if watermark file exists
    if not os.path.exists(args.watermark):
        log_print(f"ERROR: Watermark file not found at {args.watermark}")
        sys.exit(1)
        
    # Check if base directory exists
    if not os.path.exists(args.base_dir):
        log_print(f"ERROR: Base directory not found at {args.base_dir}")
        sys.exit(1)
        
    # Check if target CSV exists
    if not os.path.exists(args.target_csv):
        log_print(f"ERROR: Target CSV file not found at {args.target_csv}")
        sys.exit(1)
    
    process_target_files(
        args.target_csv,
        args.base_dir, 
        args.watermark, 
        dry_run=args.dry_run,
        batch_size=args.batch_size,
        sleep_time=args.sleep_time
    )
    
    log_print("-" * 80)
    log_print("Watermark reapplication process completed")
    log_print("=" * 80)
