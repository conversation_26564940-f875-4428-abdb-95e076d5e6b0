import logging

logger = logging.getLogger(__name__)

def apply_category_workaround_for_swagger_bug(process_category: list[str]) -> list[str]:
    """
    Workaround for Swagger UI bug with process categories.
    """
    if len(process_category) == 1 and process_category[0].count(":") > 1:
        logger.info("Applying workaround for Swagger UI bug")
        items = process_category[0].split(",")
        processed_categories = []
        current_item = ""
        for item in items:
            if ":" in item:
                if current_item:
                    processed_categories.append(current_item.strip())
                current_item = item
            else:
                current_item += "," + item
        if current_item:
            processed_categories.append(current_item.strip())
        logger.info(f"process_category after workaround: {processed_categories}")
        return processed_categories
    return process_category
