#!/bin/bash

# NHSO Single Audit Platform - Watermark Reapplication Tool
# Step 2: Upload the server script to the server

# Variables
SERVER_USER="sunweb"
SERVER_HOST="**********"
SERVER_PASSWORD="webdevadmin"
REMOTE_PATH="~/sam/"

echo "NHSO Single Audit Platform - Watermark Reapplication Tool"
echo "Step 2: Upload server script to server"
echo ""
echo "Server: $SERVER_USER@$SERVER_HOST"
echo "Remote path: $REMOTE_PATH"
echo ""

# Create a temporary Dockerfile for the upload container
cat > Dockerfile.upload << EOF
FROM ubuntu:22.04

RUN apt-get update && apt-get install -y openssh-client sshpass

WORKDIR /app

COPY watermark-server.sh /app/watermark-server.sh

CMD sshpass -p "$SERVER_PASSWORD" ssh -o StrictHostKeyChecking=no $SERVER_USER@$SERVER_HOST "mkdir -p $REMOTE_PATH" && \
    sshpass -p "$SERVER_PASSWORD" scp -o StrictHostKeyChecking=no /app/watermark-server.sh $SERVER_USER@$SERVER_HOST:$REMOTE_PATH && \
    sshpass -p "$SERVER_PASSWORD" ssh -o StrictHostKeyChecking=no $SERVER_USER@$SERVER_HOST "chmod +x ${REMOTE_PATH}watermark-server.sh" && \
    echo "Upload completed successfully!"
EOF

# Build and run the Docker container
echo "Building upload container..."
docker build -t upload-container -f Dockerfile.upload .

if [ $? -ne 0 ]; then
    echo "ERROR: Failed to build upload container"
    rm Dockerfile.upload
    exit 1
fi

echo "Uploading watermark-server.sh to $SERVER_USER@$SERVER_HOST:$REMOTE_PATH..."
docker run --rm upload-container

if [ $? -ne 0 ]; then
    echo "ERROR: Failed to upload server script"
    rm Dockerfile.upload
    exit 1
fi

# Clean up
rm Dockerfile.upload

echo "Server script uploaded successfully to $SERVER_USER@$SERVER_HOST:$REMOTE_PATH"
