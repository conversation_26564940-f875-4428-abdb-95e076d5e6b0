#!/bin/bash

# NHSO Single Audit Platform - Watermark Reapplication Tool
# Step 1: Build and push the Docker image with target CSV file

# Variables
REGISTRY_URL="docker.geniustree.io"
REGISTRY_USERNAME="pond"
REGISTRY_PASSWORD="pondpondpassword"
IMAGE_NAME="sam-mrc-watermark"
IMAGE_TAG="v1.0.0"
TARGET_CSV_FILE=""

# Function to show usage
show_usage() {
    echo "NHSO Single Audit Platform - Watermark Reapplication Tool"
    echo "Step 1: Build and push Docker image"
    echo ""
    echo "Usage: $0 --target-csv FILE"
    echo ""
    echo "Options:"
    echo "  --target-csv FILE  Path to CSV file with target files (required)"
    echo "  --help             Show this help message"
    echo ""
    echo "Example:"
    echo "  $0 --target-csv target/RERUN_WATERMARK_JOB191.csv"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case "$1" in
        --target-csv)
            TARGET_CSV_FILE="$2"
            shift 2
            ;;
        --help)
            show_usage
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Check if target CSV file is provided
if [ -z "$TARGET_CSV_FILE" ]; then
    echo "ERROR: Target CSV file is required"
    show_usage
    exit 1
fi

# Check if target CSV file exists
if [ ! -f "$TARGET_CSV_FILE" ]; then
    echo "ERROR: Target CSV file not found: $TARGET_CSV_FILE"
    exit 1
fi

# Get the directory of the script
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
cd "$SCRIPT_DIR"

# Copy the target CSV file to the target directory
echo "Copying target CSV file to target directory..."
mkdir -p target
cp "$TARGET_CSV_FILE" "target/target_files.csv"
echo "Target CSV file copied to target/target_files.csv"

# Count the number of files to be processed (excluding the header line)
FILE_COUNT=$(grep -v "^$" "$TARGET_CSV_FILE" | grep -v "^file_url" | wc -l)
echo "Task Summary: $FILE_COUNT files will be processed"

# Log in to the Docker registry
echo "Logging into Docker registry..."
echo $REGISTRY_PASSWORD | docker login $REGISTRY_URL -u $REGISTRY_USERNAME --password-stdin

if [ $? -ne 0 ]; then
    echo "ERROR: Failed to log in to Docker registry. Please check your credentials."
    exit 1
fi

# Build the Docker image
echo "Building watermark Docker image..."
docker build -t $IMAGE_NAME -f Dockerfile.watermark .

if [ $? -ne 0 ]; then
    echo "ERROR: Failed to build Docker image"
    exit 1
fi

# Tag the Docker image
echo "Tagging Docker image..."
docker tag $IMAGE_NAME $REGISTRY_URL/$IMAGE_NAME:$IMAGE_TAG

# Push the Docker image to the registry
echo "Pushing Docker image to registry..."
docker push $REGISTRY_URL/$IMAGE_NAME:$IMAGE_TAG

if [ $? -ne 0 ]; then
    echo "ERROR: Failed to push Docker image to registry"
    exit 1
fi

echo "Watermark Docker image pushed successfully!"
echo "Image: $REGISTRY_URL/$IMAGE_NAME:$IMAGE_TAG"
echo "Files to process: $FILE_COUNT"
