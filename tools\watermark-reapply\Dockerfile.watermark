# Use the same base image as the main application
FROM python:3.12-slim

WORKDIR /app

# Install required packages
RUN pip install --no-cache-dir pypdf Pillow

# Copy the watermark script
COPY reapply_watermark.py /app/
# Copy the watermark image from the main application
COPY app/services/watermark.png /app/app/services/watermark.png
# Copy the target directory
COPY target/ /app/target/

# Create directory for logs
RUN mkdir -p /app/logs

# Make the script executable
RUN chmod +x /app/reapply_watermark.py

# Set the entrypoint to the script
ENTRYPOINT ["python", "/app/reapply_watermark.py"]

# Default command (can be overridden)
CMD ["--base-dir", "/app/uploads", "--batch-size", "50", "--sleep-time", "2"]
