from datetime import datetime
from pytz import timezone


def get_current_bangkok_datetime():
    return datetime.now().astimezone(tz=timezone("Asia/Bangkok"))


def get_thai_year(year: int | None = None):
    year = year or get_current_bangkok_datetime().year
    return year + 543


def get_thai_date_str(date: datetime | None = None, year_first: bool = True):
    date = date or get_current_bangkok_datetime()
    if year_first:
        return f"{get_thai_year(date.year)}/{date.month:02}/{date.day:02}"
    return f"{date.day:02}/{date.month:02}/{get_thai_year(date.year)}"


def get_thai_datetime_str(datetime: datetime | None = None, year_first: bool = True):
    datetime = datetime or get_current_bangkok_datetime()
    return f"{get_thai_date_str(datetime, year_first)} {datetime.hour:02}:{datetime.minute:02}:{datetime.second:02}"
