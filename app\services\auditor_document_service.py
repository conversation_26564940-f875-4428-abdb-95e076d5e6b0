from fastapi import H<PERSON>PException, UploadFile
from sqlmodel import Session
from app.models import SamMasterAuditorDocument
from app.services.datetime_service import get_current_bangkok_datetime
from app.services.pdf_service import save_original_files, get_base_path
import os


class AuditorDocumentService:
    @staticmethod
    def create_auditor_documents(
        session: Session,
        master_auditor_id: int,
        uploaded_files: list[UploadFile],
        created_by: str,
    ) -> list[SamMasterAuditorDocument]:
        try:
            documents = []
            base_path = get_base_path(master_auditor_id, "auditor_doc")
            file_paths = save_original_files(uploaded_files, base_path)

            for file_path in file_paths:
                file_name = os.path.basename(file_path)
                file_size = str(os.path.getsize(file_path))
                file_type = uploaded_files[file_paths.index(file_path)].content_type

                document_data = {
                    "master_auditor_id": master_auditor_id,
                    "file_name": file_name,
                    "file_path": file_path,
                    "file_size": file_size,
                    "file_type": file_type,
                    "status": "A",
                    "created_date": get_current_bangkok_datetime(),
                    "created_by": created_by,
                }

                document = SamMasterAuditorDocument.create(
                    session, commit=False, **document_data
                )
                documents.append(document)

            session.commit()
            # Retrieve documents from the database again after commit
            documents = [SamMasterAuditorDocument.read(session, doc.id) for doc in documents]
            documents = [doc for doc in documents if doc is not None]
            return documents

        except Exception as e:
            session.rollback()
            raise HTTPException(
                status_code=500,
                detail=f"An error occurred while creating auditor documents: {str(e)}",
            )
    @staticmethod
    def get_auditor_document(
        session: Session,
        document_id: int,
    ) -> SamMasterAuditorDocument | None:
        return SamMasterAuditorDocument.read(session, document_id)
