from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>, UploadFile
from sqlmodel import Session, select, Sequence
import io
import logging

from app.models import *
from app.services.pdf_service import (
    get_base_path,
    save_original_files,
    split_pdf_files,
    apply_watermark_to_category_files,
    validate_pdf_split,
    get_pdf_page_count,
)
from app.services.datetime_service import get_current_bangkok_datetime

logger = logging.getLogger(__name__)


class JobMedicalRecordService:
    @staticmethod
    def expire_previous_records(
        session: Session,
        job_audit_id: int,
        audit_transaction_id: str,
        job_state: int,
        expired_by: str,
        commit: bool = True,
    ) -> None:
        """
        Expires all previous medical records, their processes, and categories for the given job_audit_id, audit_transaction_id and job_state.
        """
        previous_records = JobMedicalRecordService.list_job_medical_records(
            session, 
            job_audit_id=job_audit_id,
            audit_transaction_id=audit_transaction_id, 
            job_state=job_state, 
            active_only=True
        )

        for record in previous_records:  # type: ignore
            # Expire the medical record
            SamJobMedicalRecord.update(
                session,
                expired_by,
                record.id,
                commit=False,
                status="E",
                updated_date=get_current_bangkok_datetime(),
                updated_by=expired_by,
            )

            # Expire all processes
            for process in record.processes:
                SamJobMedicalRecordProcess.update(
                    session,
                    expired_by,
                    process.id,
                    commit=False,
                    status="E",
                    updated_date=get_current_bangkok_datetime(),
                    updated_by=expired_by,
                )

            # Expire all categories
            for category in record.categories:
                SamJobMedicalRecordCategory.update(
                    session,
                    expired_by,
                    category.id,
                    commit=False,
                    status="E",
                    updated_date=get_current_bangkok_datetime(),
                    updated_by=expired_by,
                )

        if commit:
            session.commit()

    @staticmethod
    def create_or_update_job_medical_record(
        session: Session,
        job_audit_id: int,
        audit_transaction_id: str,
        job_state: int,
        file_process_type: str,
        medical_record_status: str,
        uploaded_files: list[UploadFile] | None,
        process_category: list[str],
        created_by: str,
    ) -> SamJobMedicalRecordPublicWithRelated:
        try:
            # Get previous records
            previous_records = JobMedicalRecordService.list_job_medical_records(
                session,
                job_audit_id=job_audit_id,
                audit_transaction_id=audit_transaction_id,
                job_state=job_state,
                active_only=True,
            )
            previous_record = (
                max(previous_records, key=lambda x: x.created_date)  # type: ignore
                if previous_records
                else None
            )
            logger.info(f"previous_record: {previous_record}")

            # Expire previous records
            JobMedicalRecordService.expire_previous_records(
                session, job_audit_id, audit_transaction_id, job_state, created_by, commit=False
            )

            # Create new medical record
            record_data = JobMedicalRecordService.prepare_record_data(
                job_audit_id,
                job_state,
                audit_transaction_id,
                file_process_type,
                medical_record_status,
                created_by,
            )
            medical_record = SamJobMedicalRecord.create(
                session, commit=False, **record_data
            )
            logger.info(f"medical_record: {medical_record}")

            if len(process_category) == 0 and previous_record is not None:
                # User intent is to update the existing record
                # with all files from previous record removed
                # Commit all changes
                session.commit()

                medical_record = JobMedicalRecordService.get_job_medical_record(
                    session, medical_record.id
                )
                logger.info(f"medical_record after commit: {medical_record}")
                if medical_record is None:
                    raise HTTPException(status_code=404, detail="Medical record not saved")

                return medical_record
            
            if len(process_category) == 0 and previous_record is None:
                # Return error, process_category is required for creating new record
                # Creating new record without any files is not allowed
                raise HTTPException(status_code=400, detail="Process category is required for creating new record")

            # Combine new uploaded files with files from previous record
            combined_files = JobMedicalRecordService.combine_files(
                uploaded_files, process_category, previous_record
            )

            # Validate combined files
            JobMedicalRecordService.validate_uploaded_files(
                combined_files, process_category
            )

            # Create processes
            processes = JobMedicalRecordService.create_medical_record_processes(
                session,
                medical_record.id,
                job_audit_id,
                job_state,
                audit_transaction_id,
                combined_files,
                process_category,
                created_by,
                previous_record,
                commit=False,
            )
            medical_record.processes = processes
            logger.info(f"processes: {processes}")

            # Save new files and update file paths
            new_file_paths = JobMedicalRecordService.save_new_files(
                combined_files, medical_record.id, job_state
            )
            JobMedicalRecordService.update_process_file_paths(
                session, medical_record.id, new_file_paths, commit=False
            )

            # Split PDF files
            category_files = split_pdf_files(
                combined_files,
                process_category,
                medical_record.id,
                job_state,
            )
            logger.info(f"category_files: {category_files}")

            # Create categories
            categories = JobMedicalRecordService.create_medical_record_categories(
                session,
                medical_record.id,
                category_files,
                created_by,
                job_audit_id,
                job_state,
                audit_transaction_id,
                commit=False,
            )
            medical_record.categories = categories
            logger.info(f"categories: {categories}")

            # Apply watermark
            watermark_path = "./app/services/watermark.png"
            apply_watermark_to_category_files(category_files, watermark_path)

            # Commit all changes
            session.commit()

            medical_record = JobMedicalRecordService.get_job_medical_record(
                session, medical_record.id
            )
            logger.info(f"medical_record after commit: {medical_record}")
            if medical_record is None:
                raise HTTPException(status_code=404, detail="Medical record not saved")

            return medical_record

        except HTTPException as e:
            raise e
        except Exception as e:
            logger.error(e)
            raise HTTPException(
                status_code=500,
                detail=f"An unexpected error occurred: {str(e)}",
            )

    @staticmethod
    def combine_files(
        uploaded_files: list[UploadFile] | None,
        process_category: list[str],
        previous_record: SamJobMedicalRecord | None,
    ) -> list[UploadFile]:
        combined_files = []
        uploaded_file_dict = {
            file.filename: file for file in (uploaded_files or []) if file.filename
        }

        for category in process_category:
            filename, _ = category.split(":", 1)
            if filename in uploaded_file_dict:
                combined_files.append(uploaded_file_dict[filename])
            elif previous_record:
                previous_process = next(
                    (p for p in previous_record.processes if p.file_name == filename),
                    None,
                )
                if previous_process and previous_process.file_path:
                    with open(previous_process.file_path, "rb") as file:
                        content = file.read()
                    combined_files.append(UploadFile(filename=filename, file=io.BytesIO(content)))

        return combined_files

    @staticmethod
    def save_new_files(
        combined_files: list[UploadFile],
        job_medical_record_id: int,
        job_state: int,
    ) -> list[str]:
        base_path = get_base_path(job_medical_record_id, "medical_record", str(job_state))
        return save_original_files(combined_files, base_path)

    @staticmethod
    def validate_uploaded_files(
        uploaded_files: list[UploadFile], process_category: list[str]
    ) -> None:
        if any(":" in (file.filename or "") for file in uploaded_files):
            raise HTTPException(
                status_code=400, detail="File names must not include colons"
            )
        if not validate_pdf_split(uploaded_files, process_category):
            raise HTTPException(status_code=400, detail="Cannot split by category")

    @staticmethod
    def prepare_record_data(
        job_audit_id: int,
        job_state: int,
        audit_transaction_id: str,
        file_process_type: str,
        medical_record_status: str,
        created_by: str,
    ) -> dict:
        return {
            "job_audit_id": job_audit_id,
            "job_state": job_state,
            "audit_transaction_id": audit_transaction_id,
            "file_process_type": file_process_type,
            "medical_record_status": medical_record_status,
            "process_status": "NEW",
            "status": "A",
            "created_date": get_current_bangkok_datetime(),
            "created_by": created_by,
        }

    @staticmethod
    def prepare_response(
        job_medical_record_id: int,
        audit_transaction_id: str,
        job_state: int,
        process_category: list[str],
        uploaded_files: list[UploadFile],
    ) -> dict:
        return {
            "job_audit_medical_record_id": job_medical_record_id,
            "audit_transaction_id": audit_transaction_id,
            "job_state": job_state,
            "process_category": process_category,
            "uploaded_files": [file.filename for file in uploaded_files],
        }

    @staticmethod
    def get_job_medical_record(
        session: Session,
        job_medical_record_id: int,
        active_only: bool = True,
    ) -> SamJobMedicalRecordPublicWithRelated | None:
        query = select(SamJobMedicalRecord).where(
            SamJobMedicalRecord.id == job_medical_record_id
        )

        if active_only:
            query = query.where(SamJobMedicalRecord.status == "A")

        record = session.exec(query).first()

        if not record:
            return None

        # Filter processes and categories if active_only is True
        if active_only:
            record.processes = [p for p in record.processes if p.status == "A"]
            record.categories = [c for c in record.categories if c.status == "A"]

        logger.info(record)

        return record  # type: ignore

    @staticmethod
    def list_job_medical_records(
        session: Session,
        job_audit_id: int | None = None,
        audit_transaction_id: str | None = None,
        job_state: int | None = None,
        active_only: bool = True,
    ) -> list[SamJobMedicalRecord]:
        query = select(SamJobMedicalRecord)

        if active_only:
            query = query.where(SamJobMedicalRecord.status == "A")
        if job_audit_id:
            query = query.where(SamJobMedicalRecord.job_audit_id == job_audit_id)
        if audit_transaction_id:
            query = query.where(SamJobMedicalRecord.audit_transaction_id == audit_transaction_id)
        if job_state:
            query = query.where(SamJobMedicalRecord.job_state == job_state)

        records = session.exec(query).all()

        if active_only:
            for record in records:
                record.processes = [p for p in record.processes if p.status == "A"]
                record.categories = [c for c in record.categories if c.status == "A"]

        return list(records)

    @staticmethod
    def get_sam_job_medical_record_process(
        session: Session,
        sam_job_medical_record_process_id: int,
    ) -> SamJobMedicalRecordProcess | None:
        return session.get(
            SamJobMedicalRecordProcess, sam_job_medical_record_process_id
        )

    @staticmethod
    def create_medical_record_processes(
        session: Session,
        job_medical_record_id: int,
        job_audit_id: int,
        job_state: int,
        audit_transaction_id: str,
        uploaded_files: list[UploadFile],
        process_category: list[str],
        created_by: str,
        previous_record: SamJobMedicalRecord | None,
        commit: bool = False,
    ) -> list[SamJobMedicalRecordProcess]:
        processes = []

        # Create a dictionary for quick file lookup
        file_dict = {file.filename: file for file in uploaded_files if file.filename}

        for category in process_category:
            filename, categories = category.split(":", 1)
            file = file_dict.get(filename)

            if file:
                process_data = {
                    "job_medical_record_id": job_medical_record_id,
                    "job_audit_id": job_audit_id,
                    "job_state": job_state,
                    "audit_transaction_id": audit_transaction_id,
                    "file_name": filename,
                    "file_size": str(file.size),
                    "file_type": file.content_type,
                    "file_process_category": categories,
                    "process_status": "NEW",
                    "status": "A",
                    "created_date": get_current_bangkok_datetime(),
                    "created_by": created_by,
                }
            elif previous_record:
                previous_process = next(
                    (p for p in previous_record.processes if p.file_name == filename),
                    None,
                )
                if previous_process:
                    process_data = {
                        "job_medical_record_id": job_medical_record_id,
                        "job_audit_id": job_audit_id,
                        "job_state": job_state,
                        "audit_transaction_id": audit_transaction_id,
                        "file_name": previous_process.file_name,
                        "file_size": previous_process.file_size,
                        "file_type": previous_process.file_type,
                        "file_path": previous_process.file_path,
                        "file_process_category": categories,
                        "process_status": "NEW",
                        "status": "A",
                        "created_date": get_current_bangkok_datetime(),
                        "created_by": created_by,
                    }
                else:
                    logger.info(f"No previous process found for category: {category}")
                    continue
            else:
                logger.info(f"No file or previous record for category: {category}")
                continue

            process = SamJobMedicalRecordProcess.create(
                session, commit=False, **process_data
            )
            processes.append(process)

        if commit:
            session.commit()

        return processes

    @staticmethod
    def create_medical_record_categories(
        session: Session,
        job_medical_record_id: int,
        category_files: dict,
        created_by: str,
        job_audit_id: int,
        job_state: int,
        audit_transaction_id: str,
        commit: bool = False,
    ) -> list[SamJobMedicalRecordCategory]:
        categories = []
        for category, file_path in category_files.items():
            file_path_obj = Path(file_path)
            file_name = file_path_obj.name
            file_extension = file_path_obj.suffix.lower()
            file_type = {
                ".pdf": "application/pdf",
                ".jpg": "image/jpeg",
                ".jpeg": "image/jpeg",
                ".png": "image/png",
                ".tiff": "image/tiff",
                ".tif": "image/tiff",
            }.get(file_extension, "application/octet-stream")

            # Get the number of pages for PDF files
            file_page = None
            if file_type == "application/pdf":
                file_page = str(get_pdf_page_count(file_path))

            category_data = {
                "job_medical_record_id": job_medical_record_id,
                "job_audit_id": job_audit_id,
                "job_state": job_state,
                "audit_transaction_id": audit_transaction_id,
                "file_name": file_name,
                "file_size": str(file_path_obj.stat().st_size),
                "file_type": file_type,
                "file_path": str(file_path),
                "file_category": category,
                "file_page": file_page,
                "process_status": "NEW",
                "status": "A",
                "created_date": get_current_bangkok_datetime(),
                "created_by": created_by,
            }
            category = SamJobMedicalRecordCategory.create(
                session, commit=False, **category_data
            )
            categories.append(category)

        if commit:
            session.commit()
        return categories

    @staticmethod
    def update_process_file_paths(
        session: Session,
        job_medical_record_id: int,
        original_file_paths: list[str],
        commit: bool = True,
    ) -> None:
        processes = session.exec(
            select(SamJobMedicalRecordProcess).where(
                SamJobMedicalRecordProcess.job_medical_record_id
                == job_medical_record_id
            )
        ).all()

        file_path_dict = {Path(path).name: path for path in original_file_paths}

        for process in processes:
            if process.file_name in file_path_dict:
                process.file_path = file_path_dict[process.file_name]
                session.add(process)

        if commit:
            session.commit()
