import os
from sqlmodel import create_engine, Session
import logging

logger = logging.getLogger(__name__)

# Oracle connection details
DB_USER = os.getenv("DB_USER")
DB_PASSWORD = os.getenv("DB_PASSWORD")
DB_HOST = os.getenv("DB_HOST")
DB_SERVICE = os.getenv("DB_SERVICE")
DB_PORT = os.getenv("DB_PORT", "1522")
DB_RETRY_COUNT = os.getenv("DB_RETRY_COUNT", "20")
DB_RETRY_DELAY = os.getenv("DB_RETRY_DELAY", "3")
DB_PROTOCOL = os.getenv("DB_PROTOCOL", "tcps")
DB_SSL_SERVER_DN_MATCH = os.getenv("DB_SSL_SERVER_DN_MATCH", "yes")

# Create engine using oracledb with connect_args
engine = create_engine(
    "oracle+oracledb://@",
    connect_args={
        "user": DB_USER,
        "password": DB_PASSWORD,
        "host": DB_HOST,
        "service_name": DB_SERVICE,
        "port": DB_PORT,
        "retry_count": int(DB_RETRY_COUNT),
        "retry_delay": int(DB_RETRY_DELAY),
        "protocol": DB_PROTOCOL,
        "ssl_server_dn_match": DB_SSL_SERVER_DN_MATCH.lower() == "yes",
    },
    thick_mode=True,
)

logger.info("Database engine created successfully")


def test_db_connection():
    try:
        with engine.connect() as connection:
            logger.info("Database connection successful")
            return True
    except Exception as e:
        logger.error(f"Database connection failed: {str(e)}")
        return False


# Test database connection
if not test_db_connection():
    raise Exception("Unable to connect to the database")

logger.info("Database connection test passed")


def get_session():
    with Session(engine) as session:
        yield session
