#!/bin/bash

# Variables
REGISTRY_URL="docker.geniustree.io"
REGISTRY_USERNAME="pond"
REGISTRY_PASSWORD="pondpondpassword"
IMAGE_NAME="sam-mrc"
IMAGE_TAG="v0.1.0"

# Log in to the Docker registry
echo "Logging into Docker registry..."
echo $REGISTRY_PASSWORD | docker login $REGISTRY_URL -u $REGISTRY_USERNAME --password-stdin

# Build the Docker image
echo "Building Docker image..."
docker build -t $IMAGE_NAME .

# Tag the Docker image
echo "Tagging Docker image..."
docker tag $IMAGE_NAME $REGISTRY_URL/$IMAGE_NAME:$IMAGE_TAG

# Push the Docker image to the registry
echo "Pushing Docker image to registry..."
docker push $REGISTRY_URL/$IMAGE_NAME:$IMAGE_TAG

echo "Docker image pushed successfully!"
