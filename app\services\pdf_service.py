from pypdf import Pd<PERSON><PERSON><PERSON><PERSON>, PdfWriter, PageObject
import re
import os
from fastapi import HTTPEx<PERSON>, UploadFile
from PIL import Image
import io
from pathlib import Path

from .datetime_service import get_thai_date_str


def validate_pdf_split(uploaded_files, process_category) -> bool:
    file_category_map = {
        f"{pc.split(':')[0]}": pc.split(":")[1] for pc in process_category
    }

    for file in uploaded_files:
        pdf_reader = PdfReader(file.file)
        category_info = file_category_map.get(file.filename)

        if not category_info:
            continue

        categories = re.findall(r"\[(\d+)=([\d,-]*)\]", category_info)

        for _, pages in categories:
            page_ranges = pages.split(",")
            for page_range in page_ranges:
                try:
                    if page_range == "":
                        continue
                    elif "-" in page_range:
                        start, end = map(int, page_range.split("-"))
                        if start < 1 or end > len(pdf_reader.pages):
                            raise IndexError
                    else:
                        page_num = int(page_range) - 1
                        if page_num < 0 or page_num >= len(pdf_reader.pages):
                            raise IndexError
                except ValueError:
                    raise HTTPException(
                        status_code=400, detail=f"Invalid page range: {page_range}"
                    )
                except IndexError:
                    raise HTTPException(
                        status_code=400,
                        detail=f"Page number out of range for file: {file.filename}",
                    )
        file.file.seek(0)  # Reset file pointer for reuse
    return True


def split_pdf_files(
    uploaded_files, process_category, job_audit_medical_record_id, job_state
) -> dict:
    category_writers = {}
    file_category_map = {
        f"{pc.split(':')[0]}": pc.split(":")[1] for pc in process_category
    }

    for file in uploaded_files:
        pdf_reader = PdfReader(file.file)
        category_info = file_category_map.get(file.filename)

        if not category_info:
            continue

        categories = re.findall(r"\[(\d+)=([\d,-]*)\]", category_info)

        for category, pages in categories:
            if category not in category_writers:
                category_writers[category] = PdfWriter()

            page_ranges = pages.split(",")
            for page_range in page_ranges:
                if page_range == "":
                    for page_num in range(len(pdf_reader.pages)):
                        category_writers[category].add_page(pdf_reader.pages[page_num])
                elif "-" in page_range:
                    start, end = map(int, page_range.split("-"))
                    for page_num in range(start - 1, min(end, len(pdf_reader.pages))):
                        category_writers[category].add_page(pdf_reader.pages[page_num])
                else:
                    page_num = int(page_range) - 1
                    if 0 <= page_num < len(pdf_reader.pages):
                        category_writers[category].add_page(pdf_reader.pages[page_num])

    base_path = get_base_path(job_audit_medical_record_id, job_state)
    category_files = save_category_files(category_writers, base_path)

    return category_files


def get_base_path(
    identifier: int, folder_type: str, sub_folder: str | None = None
) -> str:
    base_path = f"./uploads/{get_thai_date_str()}/{folder_type}/{identifier}"
    if sub_folder:
        base_path = f"{base_path}/{sub_folder}"
    os.makedirs(base_path, exist_ok=True)
    return base_path


def save_original_files(uploaded_files: list[UploadFile], base_path: str) -> list[str]:
    saved_file_paths = []
    watermark_path = "./app/services/watermark.png"  # Path to the watermark image

    for file in uploaded_files:
        original_path = f"{base_path}/{file.filename}"
        os.makedirs(os.path.dirname(original_path), exist_ok=True)

        # Apply watermark to PDF files
        if file.content_type == "application/pdf":
            pdf_reader = PdfReader(file.file)
            pdf_writer = PdfWriter()

            watermark_pdf = image_to_pdf(Path(watermark_path))
            watermark_page = watermark_pdf.pages[0]

            for page in pdf_reader.pages:
                page.merge_page(watermark_page, over=True)  # Changed to over=True
                pdf_writer.add_page(page)

            with open(original_path, "wb") as output_file:
                pdf_writer.write(output_file)
        else:
            with open(original_path, "wb") as output_file:
                file.file.seek(0)  # Ensure we're at the start of the file
                output_file.write(file.file.read())

        saved_file_paths.append(original_path)
        file.file.seek(0)  # Reset file pointer for potential reuse

    return saved_file_paths


def save_category_files(
    category_writers: dict[str, PdfWriter], base_path: str
) -> dict[str, str]:
    category_files = {}
    for category, writer in category_writers.items():
        output_path = f"{base_path}/{category}/category_{category}.pdf"
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        with open(output_path, "wb") as output_file:
            writer.write(output_file)
        category_files[category] = output_path
    return category_files


def image_to_pdf(stamp_img: os.PathLike) -> PdfReader:
    img = Image.open(stamp_img)
    img_as_pdf = io.BytesIO()
    img.save(img_as_pdf, "pdf")
    return PdfReader(img_as_pdf)


def apply_watermark_to_category_files(
    category_files: dict[str, str], watermark_path: str
) -> None:
    watermark_pdf = image_to_pdf(Path(watermark_path))
    watermark_page = watermark_pdf.pages[0]

    for category_file in category_files.values():
        add_watermark_to_pdf(category_file, category_file, watermark_page)


def add_watermark_to_pdf(input_pdf_path, output_pdf_path, watermark_page):
    # Open the PDF
    pdf_writer = PdfWriter(clone_from=input_pdf_path)

    # Iterate through each page
    for page in pdf_writer.pages:
        # Merge watermark with the page
        page.merge_page(watermark_page, over=True)  # Changed to over=True to match save_original_files

    # Write the output PDF
    with open(output_pdf_path, "wb") as output_file:
        pdf_writer.write(output_file)


def get_pdf_page_count(file_path: str) -> int:
    with open(file_path, "rb") as pdf_file:
        pdf_reader = PdfReader(pdf_file)
        return len(pdf_reader.pages)
