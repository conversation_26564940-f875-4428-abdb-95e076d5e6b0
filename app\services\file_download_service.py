import os
import zipfile
import tempfile
import logging
from pathlib import Path
from typing import List, Dict, Any
from sqlmodel import Session, text
from fastapi import HTTPException
import shutil

logger = logging.getLogger(__name__)


class FileDownloadService:
    """Service for downloading files and creating zip archives"""
    
    @staticmethod
    def get_files_by_pa_kidney_id(session: Session, pa_kidney_id: str) -> List[Dict[str, Any]]:
        """
        Get files from SAM_PA_KIDNEY_UPLOAD table based on PA_KIDNEY_ID and STATUS = 'A'
        
        Args:
            session: Database session
            pa_kidney_id: PA Kidney ID to filter by
            
        Returns:
            List of file records with FILE_PATH and other details
        """
        try:
            query = text("""
                SELECT * FROM SAM_PA_KIDNEY_UPLOAD 
                WHERE PA_KIDNEY_ID = :pa_kidney_id AND STATUS = 'A'
            """)
            
            result = session.execute(query, {"pa_kidney_id": pa_kidney_id})
            files = []
            
            for row in result:
                file_record = dict(row._mapping)
                files.append(file_record)
            
            logger.info(f"Found {len(files)} files for PA_KIDNEY_ID: {pa_kidney_id}")
            return files
            
        except Exception as e:
            logger.error(f"Error querying files for PA_KIDNEY_ID {pa_kidney_id}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")
    
    @staticmethod
    def create_zip_from_files(files: List[Dict[str, Any]], zip_filename: str = None) -> str:
        """
        Create a zip file containing all the files from the database records
        
        Args:
            files: List of file records from database
            zip_filename: Optional custom zip filename
            
        Returns:
            Path to the created zip file
        """
        if not files:
            raise HTTPException(status_code=400, detail="No files found to download")
        
        # Create temporary directory for zip file
        temp_dir = tempfile.mkdtemp()
        
        if not zip_filename:
            zip_filename = f"pa_kidney_files_{files[0].get('pa_kidney_id', 'unknown')}.zip"
        
        zip_path = os.path.join(temp_dir, zip_filename)
        
        try:
            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                added_files = 0
                missing_files = []
                invalid_paths = []
                
                logger.info(f"Processing {len(files)} files for zip creation")
                
                for file_record in files:
                    file_path = file_record.get('file_path')
                    
                    if not file_path:
                        logger.warning(f"Missing file_path in record: {file_record}")
                        invalid_paths.append({
                            "record_id": file_record.get('id', 'N/A'),
                            "pa_kidney_id": file_record.get('pa_kidney_id', 'N/A'),
                            "file_name": file_record.get('file_name', 'N/A'),
                            "reason": "Missing file_path",
                            "file_path": file_path
                        })
                        continue
                    
                    # Normalize file path
                    normalized_path = file_path.replace('\\', '/').strip()
                    
                    # Remove leading './' if present
                    if normalized_path.startswith('./'):
                        normalized_path = normalized_path[2:]
                    
                    # Check if file exists
                    if not os.path.exists(normalized_path):
                        logger.warning(f"File not found: {normalized_path}")
                        missing_files.append(normalized_path)
                        continue
                    
                    try:
                        # Create a meaningful filename for the zip
                        # Extract the last part of the path as filename
                        path_parts = normalized_path.split('/')
                        
                        # Check if this is a LAB type file (contains kidney_lab in path)
                        if 'kidney_lab' in normalized_path:
                            # For LAB files, create structure: LAB/file_name_(SEQ)
                            # Use SEQ from database record instead of lab_id from path
                            file_name = path_parts[-1]
                            name_without_ext, ext = os.path.splitext(file_name)
                            # Find the corresponding record to get SEQ
                            seq_value = None
                            for record in files:
                                if record['file_path'] == file_record['file_path']:
                                    seq_value = record.get('seq')
                                    break
                            
                            if seq_value is not None:
                                zip_internal_path = f"LAB/{name_without_ext}({seq_value}){ext}"
                            else:
                                # Fallback if SEQ not found
                                zip_internal_path = f"LAB/{file_name}"
                        else:
                            # For non-LAB files, use original structure
                            if len(path_parts) >= 2:
                                folder_name = path_parts[-2] if len(path_parts) > 1 else ""
                                file_name = path_parts[-1]
                                zip_internal_path = f"{folder_name}/{file_name}"
                            else:
                                zip_internal_path = os.path.basename(normalized_path)
                        
                        # Add file to zip
                        zipf.write(normalized_path, zip_internal_path)
                        added_files += 1
                        logger.info(f"Added file to zip: {normalized_path} -> {zip_internal_path}")
                        
                    except Exception as e:
                        logger.error(f"Error adding file {normalized_path} to zip: {str(e)}")
                        continue
                
                if added_files == 0:
                    error_detail = f"No valid files found to add to zip. "
                    if missing_files:
                        error_detail += f"Missing files: {missing_files[:3]}{'...' if len(missing_files) > 3 else ''}. "
                    if invalid_paths:
                        error_detail += f"Invalid paths: {len(invalid_paths)} records. "
                    error_detail += f"Total records processed: {len(files)}"
                    
                    logger.error(error_detail)
                    raise HTTPException(status_code=400, detail=error_detail)
                
                logger.info(f"Successfully created zip file with {added_files} files: {zip_path}")
                if missing_files:
                    logger.warning(f"Skipped {len(missing_files)} missing files")
                return zip_path
                
        except Exception as e:
            # Clean up temp directory on error
            shutil.rmtree(temp_dir, ignore_errors=True)
            logger.error(f"Error creating zip file: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error creating zip file: {str(e)}")
    
    @staticmethod
    def cleanup_temp_files(zip_path: str):
        """
        Clean up temporary files after download
        
        Args:
            zip_path: Path to the zip file to clean up
        """
        try:
            temp_dir = os.path.dirname(zip_path)
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)
                logger.info(f"Cleaned up temporary directory: {temp_dir}")
        except Exception as e:
            logger.error(f"Error cleaning up temporary files: {str(e)}")
    
    @staticmethod
    def download_pa_kidney_files(session: Session, pa_kidney_id: str) -> str:
        """
        Main method to download files for a specific PA_KIDNEY_ID
        
        Args:
            session: Database session
            pa_kidney_id: PA Kidney ID to download files for
            
        Returns:
            Path to the created zip file
        """
        try:
            # Get files from database
            files = FileDownloadService.get_files_by_pa_kidney_id(session, pa_kidney_id)
            
            if not files:
                raise HTTPException(status_code=400, detail=f"No files found for PA_KIDNEY_ID: {pa_kidney_id}")
            
            # Debug: Log file paths found in database
            logger.info(f"Files found in database for PA_KIDNEY_ID {pa_kidney_id}:")
            for i, file_record in enumerate(files):
                file_path = file_record.get('file_path', 'N/A')
                logger.info(f"  {i+1}. {file_path}")
            
            # Create zip file
            zip_path = FileDownloadService.create_zip_from_files(files)
            
            return zip_path
            
        except HTTPException:
            # Re-raise HTTP exceptions
            raise
        except Exception as e:
            logger.error(f"Unexpected error in download_pa_kidney_files: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")
    
    @staticmethod
    def debug_file_paths(session: Session, pa_kidney_id: str) -> Dict[str, Any]:
        """
        Debug method to check file paths and their existence
        
        Args:
            session: Database session
            pa_kidney_id: PA Kidney ID to check
            
        Returns:
            Dictionary with debug information
        """
        try:
            files = FileDownloadService.get_files_by_pa_kidney_id(session, pa_kidney_id)
            
            debug_info = {
                "pa_kidney_id": pa_kidney_id,
                "total_records": len(files),
                "existing_files": [],
                "missing_files": [],
                "invalid_paths": []
            }
            
            for file_record in files:
                file_path = file_record.get('file_path')
                
                if not file_path:
                    debug_info["invalid_paths"].append({
                        "record_id": file_record.get('id', 'N/A'),
                        "pa_kidney_id": file_record.get('pa_kidney_id', 'N/A'),
                        "file_name": file_record.get('file_name', 'N/A'),
                        "reason": "Missing file_path",
                        "file_path": file_path
                    })
                    continue
                
                # Normalize file path
                normalized_path = file_path.replace('\\', '/').strip()
                if normalized_path.startswith('./'):
                    normalized_path = normalized_path[2:]
                
                if os.path.exists(normalized_path):
                    debug_info["existing_files"].append({
                        "original_path": file_path,
                        "normalized_path": normalized_path,
                        "file_size": os.path.getsize(normalized_path)
                    })
                else:
                    debug_info["missing_files"].append({
                        "original_path": file_path,
                        "normalized_path": normalized_path
                    })
            
            return debug_info
            
        except Exception as e:
            logger.error(f"Error in debug_file_paths: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Debug error: {str(e)}")
    
    @staticmethod
    def get_table_structure(session: Session) -> Dict[str, Any]:
        """
        Get the structure of SAM_PA_KIDNEY_UPLOAD table
        
        Args:
            session: Database session
            
        Returns:
            Dictionary with table structure information
        """
        try:
            # Get column information
            query = text("""
                SELECT column_name, data_type, nullable, data_default
                FROM user_tab_columns 
                WHERE table_name = 'SAM_PA_KIDNEY_UPLOAD'
                ORDER BY column_id
            """)
            
            result = session.execute(query)
            columns = []
            
            for row in result:
                column_info = dict(row._mapping)
                columns.append(column_info)
            
            # Get sample data
            sample_query = text("""
                SELECT * FROM SAM_PA_KIDNEY_UPLOAD 
                WHERE ROWNUM <= 5
                ORDER BY ID
            """)
            
            sample_result = session.execute(sample_query)
            sample_data = []
            
            for row in sample_result:
                sample_record = dict(row._mapping)
                sample_data.append(sample_record)
            
            return {
                "table_name": "SAM_PA_KIDNEY_UPLOAD",
                "columns": columns,
                "sample_data": sample_data
            }
            
        except Exception as e:
            logger.error(f"Error getting table structure: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error getting table structure: {str(e)}") 