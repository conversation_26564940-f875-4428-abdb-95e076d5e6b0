# NHSO Single Audit Platform - Watermark Reapplication Tool

This tool reapplies watermarks to PDF files listed in a target CSV file. It's designed to have minimal impact on the server environment by running in a Docker container.

## Directory Structure

All watermark reapplication tool files are located in the `tools/watermark-reapply/` directory:

- `reapply_watermark.py` - The main Python script that handles the watermark reapplication
- `Dockerfile.watermark` - Defines the Docker image for the watermark tool
- `watermark-deploy.sh` - <PERSON><PERSON>t to build and push the Docker image to your registry
- `watermark-server.sh` - The script that runs on the server
- `docker-upload.sh` - Script to upload the server script to the server
- `target/` - Directory containing the target CSV file with list of files to process
- `target/target_files.csv` - CSV file with list of file URLs to process

## Deployment Instructions

The watermark reapplication process is divided into 3 simple steps:

### Step 1: Build and Push the Docker Image with Target CSV

On your development machine, navigate to the tool directory and run:

```bash
cd tools/watermark-reapply
./watermark-deploy.sh --target-csv path/to/your/files.csv
```

This will:
1. Copy the target CSV file to the target directory
2. Build the Docker image with the target CSV file
3. Push the image to your registry at `docker.geniustree.io/sam-mrc-watermark:v1.0.0`
4. Show a summary of how many files will be processed

Example:
```bash
./watermark-deploy.sh --target-csv target/RERUN_WATERMARK_JOB191.csv
```

### Step 2: Upload the Server Script

After building and pushing the Docker image, upload the server script:

```bash
cd tools/watermark-reapply
./docker-upload.sh
```

This will:
1. Upload the `watermark-server.sh` script to the server at `~/sam/`
2. Make the script executable

### Step 3: Run the Script on the Server

1. SSH into the server:
   ```bash
   ssh sunweb@**********
   ```

2. Navigate to the directory where the script was uploaded:
   ```bash
   cd ~/sam
   ```

3. Run a dry run first to verify what would be done:
   ```bash
   ./watermark-server.sh --dry-run
   ```

4. If everything looks good, run the actual watermark reapplication:
   ```bash
   ./watermark-server.sh
   ```

5. You can customize the batch size and sleep time:
   ```bash
   ./watermark-server.sh --batch-size 200 --sleep-time 0.5
   ```

The server script will:
1. Pull the Docker image from your registry
2. Run the container with the appropriate volume mounts
3. Process the files listed in the CSV file
4. Show progress reports and a final summary

## Usage Options

```
Usage: ./watermark-server.sh [OPTIONS]

Options:
  --dry-run         Run in dry-run mode (no changes will be made)
  --batch-size N    Process N files before sleeping (default: 100)
  --sleep-time N    Sleep N seconds between batches (default: 1)
  --target-csv PATH Path to CSV file with target files (default: /app/target/target_files.csv)
  --help            Show this help message

Example:
  ./watermark-server.sh --dry-run                  # Test run without making changes
  ./watermark-server.sh                            # Run with default settings
  ./watermark-server.sh --batch-size 200 --sleep-time 0.5  # Custom batch size and sleep time
  ./watermark-server.sh --target-csv /path/to/files.csv  # Use a custom target CSV file
```

## Monitoring

The script creates a `watermark-logs` directory where log files are stored. You can monitor the progress and check for any errors in these log files.

## Performance Considerations

- The tool processes files in batches (default 100) with a sleep time between batches (default 1 second)
- These parameters can be adjusted to reduce server load or increase performance
- For 37,000+ files, the process is estimated to take 5-10 hours depending on server performance
- Consider running during off-peak hours for minimal impact

## Target CSV Format

The target CSV file should have the following format:

```
file_url
http://example.com/app/uploads/2023/01/01/medical_record/12345/sample.pdf
/uploads/2023/01/01/auditor_doc/67890/document.pdf
2023/01/01/medical_record/12345/1/category_1.pdf
```

- The first line is a header (will be skipped)
- Each subsequent line contains a file URL or path
- URLs can be full URLs (starting with http) or relative paths
- The script will convert URLs to file paths relative to the base directory
