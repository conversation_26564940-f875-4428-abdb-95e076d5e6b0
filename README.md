# AI OCR Module for NHSO Single Audit Platform

This module provides advanced Optical Character Recognition (OCR) capabilities for the NHSO Single Audit Platform using artificial intelligence techniques.

## Features

- High-accuracy text extraction from various document types using Tesseract OCR
- Support for Thai and English languages
- Integration with the NHSO Single Audit Platform
- NLP-based OCR cleanup with autocorrect functionality
- AI-powered data extraction using MedLLaMA2 through Ollama

## Process Flow

1. **OCR**: Extract raw text from documents using Tesseract
2. **NLP Cleanup**: Apply Natural Language Processing techniques to clean and correct OCR output using either Neuspell or Autocorrect
3. **AI Data Extraction**: Utilize MedLLaMA2 through Ollama to extract meaningful data from the cleaned text

## Installation

1. Clone the repository
2. Install dependencies: `pip install -r requirements.txt`
3. Install Oracle Instant Client
3.1 Oracle Instant Client zip file is in the repository
3.2 Unzip the file and place it in the /opt/oracle using
   `sudo unzip -d /opt/oracle instantclient-basic-linux.x64-*********.0dbru.zip`
3.3 Update apt-get using `sudo apt-get update`
3.4 Install libaio using `sudo apt-get -y install libaio1`
3.5 Configure path using 
   `sudo sh -c "echo /opt/oracle/instantclient_19_24 > /etc/ld.so.conf.d/oracle-instantclient.conf"`
   `sudo ldconfig`
3. Set up environment variables (see `.env.example`)
5. Install Ollama: [Ollama installation instructions](https://github.com/ollama/ollama)
6. Pull the MedLLaMA2 model: `ollama pull medllama2`

## Usage

[Add detailed usage instructions here, including how to run the OCR, NLP cleanup, and AI data extraction processes]

## Model Information

- **OCR Engine**: Tesseract OCR
- **NLP Cleanup**: 
  - Option 1: [Neuspell](https://github.com/neuspell/neuspell)
  - Option 2: [Autocorrect](https://github.com/filyp/autocorrect)
- **AI Data Extraction**: MedLLaMA2 (Large Language Model fine-tuned for medical domain) via Ollama

## Configuration

- Choose between Neuspell and Autocorrect for the NLP cleanup step in the configuration file or environment variables.
- Ensure Ollama is running and the MedLLaMA2 model is available before starting the AI data extraction process.

## Contributing

[Add contribution guidelines here]

## License

[Add license information here]