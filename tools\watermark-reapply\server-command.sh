#!/bin/bash

# NHSO Single Audit Platform - Server Command Runner
# This script runs a command on the server and then removes itself

# Variables
SERVER_USER="sunweb"
SERVER_HOST="**********"
SERVER_PASSWORD="webdevadmin"
COMMAND=""

# Function to show usage
show_usage() {
    echo "NHSO Single Audit Platform - Server Command Runner"
    echo ""
    echo "Usage: $0 --command \"COMMAND\""
    echo ""
    echo "Options:"
    echo "  --command \"CMD\"  Command to run on the server (required)"
    echo "  --help           Show this help message"
    echo ""
    echo "Example:"
    echo "  $0 --command \"ps aux | grep find\""
    echo "  $0 --command \"kill -9 12345\""
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case "$1" in
        --command)
            COMMAND="$2"
            shift 2
            ;;
        --help)
            show_usage
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Check if command is provided
if [ -z "$COMMAND" ]; then
    echo "ERROR: Command is required"
    show_usage
    exit 1
fi

# Create a temporary SSH script
SSH_SCRIPT=$(mktemp)
chmod +x $SSH_SCRIPT

cat > $SSH_SCRIPT << EOF
#!/usr/bin/expect -f
set timeout -1
spawn ssh $SERVER_USER@$SERVER_HOST

# Handle the password prompt
expect "password:"
send "$SERVER_PASSWORD\r"

# Wait for the prompt
expect "$ "

# Run the command
send "$COMMAND\r"
expect "$ "

# Exit
send "exit\r"
expect eof
EOF

# Check if expect is installed
if ! command -v expect &> /dev/null; then
    echo "Installing expect..."
    sudo apt-get update && sudo apt-get install -y expect
fi

# Run the SSH script
echo "Connecting to server to run command: $COMMAND"
expect $SSH_SCRIPT

# Clean up
rm $SSH_SCRIPT

echo "Command execution completed!"

# Remove this script since it's a one-time operation
echo "Removing this script as it's no longer needed..."
rm -- "$0"
echo "Script removed."
