#!/bin/bash

# NHSO Single Audit Platform - Watermark Reapplication Tool
# This script pulls and runs the watermark reapplication container

# Variables
REGISTRY_URL="docker.geniustree.io"
REGISTRY_USERNAME="pond"
REGISTRY_PASSWORD="pondpondpassword"
IMAGE_NAME="sam-mrc-watermark"
IMAGE_TAG="v1.0.0"
FULL_IMAGE_NAME="$REGISTRY_URL/$IMAGE_NAME:$IMAGE_TAG"
CONTAINER_ID=""

# Function to handle Ctrl+C and other termination signals
cleanup() {
    echo ""
    echo "Received termination signal. Cleaning up..."
    
    if [ -n "$CONTAINER_ID" ]; then
        echo "Stopping Docker container..."
        docker stop "$CONTAINER_ID" > /dev/null 2>&1
        echo "Container stopped."
    fi
    
    echo "Exiting."
    exit 1
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

# Function to show usage
show_usage() {
    echo "NHSO Single Audit Platform - Watermark Reapplication Tool"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --dry-run         Run in dry-run mode (no changes will be made)"
    echo "  --batch-size N    Process N files before sleeping (default: 100)"
    echo "  --sleep-time N    Sleep N seconds between batches (default: 1)"
    echo "  --target-csv PATH Path to CSV file with target files (default: /app/target/target_files.csv)"
    echo "  --help            Show this help message"
    echo ""
    echo "Example:"
    echo "  $0 --dry-run                  # Test run without making changes"
    echo "  $0                            # Run with default settings"
    echo "  $0 --batch-size 200 --sleep-time 0.5  # Custom batch size and sleep time"
    echo "  $0 --target-csv /path/to/files.csv  # Use a custom target CSV file"
}

# Parse command line arguments
DRY_RUN=""
BATCH_SIZE="100"
SLEEP_TIME="1"
TARGET_CSV="/app/target/target_files.csv"

while [[ $# -gt 0 ]]; do
    case "$1" in
        --dry-run)
            DRY_RUN="--dry-run"
            shift
            ;;
        --batch-size)
            BATCH_SIZE="$2"
            shift 2
            ;;
        --sleep-time)
            SLEEP_TIME="$2"
            shift 2
            ;;
        --target-csv)
            TARGET_CSV="$2"
            shift 2
            ;;
        --help)
            show_usage
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Log in to the Docker registry
echo "Logging into Docker registry..."
echo $REGISTRY_PASSWORD | docker login $REGISTRY_URL -u $REGISTRY_USERNAME --password-stdin

if [ $? -ne 0 ]; then
    echo "ERROR: Failed to log in to Docker registry. Please check your credentials."
    exit 1
fi

# Pull the latest image
echo "Pulling the watermark image..."
docker pull $FULL_IMAGE_NAME

if [ $? -ne 0 ]; then
    echo "ERROR: Failed to pull the Docker image. Please check your network connection."
    exit 1
fi

# Create a logs directory if it doesn't exist
mkdir -p ./watermark-logs

# Check if we should do a dry run
if [ -n "$DRY_RUN" ]; then
    echo "Running in DRY RUN mode (no changes will be made)..."
    echo "Starting Docker container in dry run mode..."
    CONTAINER_ID=$(docker run -d \
        -v /singleaudit/uploads:/app/uploads \
        -v $(pwd)/watermark-logs:/app/logs \
        $FULL_IMAGE_NAME \
        --dry-run \
        --batch-size $BATCH_SIZE \
        --sleep-time $SLEEP_TIME \
        --target-csv $TARGET_CSV \
        --base-dir /app/uploads)
    
    echo "Container started with ID: $CONTAINER_ID"
    echo "Press Ctrl+C at any time to stop the process"
    echo ""
    
    # Follow the container logs
    docker logs -f "$CONTAINER_ID"
    
    # Wait for the container to finish
    docker wait "$CONTAINER_ID" > /dev/null
    
    # Get the exit code
    EXIT_CODE=$(docker inspect "$CONTAINER_ID" --format='{{.State.ExitCode}}')
    
    # Remove the container
    docker rm "$CONTAINER_ID" > /dev/null
    
    # Reset container ID
    CONTAINER_ID=""
    
    # Check exit code
    if [ "$EXIT_CODE" -ne 0 ]; then
        echo "Process failed with exit code $EXIT_CODE"
        exit $EXIT_CODE
    fi
else
    # Ask for confirmation before proceeding
    read -p "This will reapply watermarks to PDF files listed in the target CSV. Continue? (y/n) " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo "Starting watermark reapplication process..."
        echo "Starting Docker container..."
        CONTAINER_ID=$(docker run -d \
            -v /singleaudit/uploads:/app/uploads \
            -v $(pwd)/watermark-logs:/app/logs \
            $FULL_IMAGE_NAME \
            --batch-size $BATCH_SIZE \
            --sleep-time $SLEEP_TIME \
            --target-csv $TARGET_CSV \
            --base-dir /app/uploads)
        
        echo "Container started with ID: $CONTAINER_ID"
        echo "Press Ctrl+C at any time to stop the process"
        echo ""
        
        # Follow the container logs
        docker logs -f "$CONTAINER_ID"
        
        # Wait for the container to finish
        docker wait "$CONTAINER_ID" > /dev/null
        
        # Get the exit code
        EXIT_CODE=$(docker inspect "$CONTAINER_ID" --format='{{.State.ExitCode}}')
        
        # Remove the container
        docker rm "$CONTAINER_ID" > /dev/null
        
        # Reset container ID
        CONTAINER_ID=""
        
        # Check exit code
        if [ "$EXIT_CODE" -ne 0 ]; then
            echo "Process failed with exit code $EXIT_CODE"
            exit $EXIT_CODE
        else
            echo "Watermark reapplication completed successfully."
            echo "Log files are available in the ./watermark-logs directory."
        fi
    else
        echo "Operation cancelled."
    fi
fi
