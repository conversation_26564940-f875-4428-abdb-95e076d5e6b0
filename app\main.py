import time
from contextlib import asynccontextmanager
from dotenv import load_dotenv
from fastapi import FastAPI, Depends, UploadFile, File, Form, HTTPException
from fastapi.responses import FileResponse
from sqlmodel import Session, text
from os import path
from urllib.parse import unquote
import logging
import os

# Load environment variables before any other imports
load_dotenv()

# Ensure logs directory exists
os.makedirs('./logs', exist_ok=True)

# Configure logging
log_file = './logs/app.log'
file_handler = logging.FileHandler(
    filename=log_file,
    encoding='utf-8'
)

# Configure logging
logging.basicConfig(
    level=logging.ERROR,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        file_handler
    ]
)
logger = logging.getLogger(__name__)

from app.database import get_session
from app.models import *
from app.services.api_service import apply_category_workaround_for_swagger_bug
from app.services.job_medical_record_service import JobMedicalRecordService
from app.services.auditor_document_service import AuditorDocumentService
from app.services.pa_kidney_upload_service import PaKidneyUploadService
from app.services.file_download_service import FileDownloadService


app = FastAPI()  # Initialize FastAPI application


@asynccontextmanager
async def lifespan(app):
    logger.info("Starting application...")
    # init_db()  # Initialize the database
    yield
    logger.info("Shutting down application...")


@app.get("/")
def read_root():
    logger.info("Received request to root endpoint")
    response = {"message": "Welcome to FastAPI with SQLModel and Celery!"}
    logger.info(f"Response: {response}")
    return response


@app.get("/tables")
def list_tables(session: Session = Depends(get_session)):
    logger.info("Received request to list tables")
    try:
        start_time = time.time()
        table_names = session.execute(text("SELECT table_name FROM user_tables"))
        result = {"tables": table_names.scalars().all()}
        response_time = time.time() - start_time
        logger.info(f"Successfully listed tables. Response time: {response_time:.2f} seconds")
        logger.info(f"Response: {result}")
        return result
    except Exception as e:
        logger.error(f"Error listing tables: {str(e)}")
        raise


@app.post("/job-medical-record", response_model=SamJobMedicalRecordPublicWithRelated)
async def create_or_update_job_medical_record(
    job_audit_id: int = Form(..., description="The ID of the job audit."),
    audit_transaction_id: str = Form(..., description="The ID of the audit transaction."),
    job_state: int = Form(..., description="The current state of the job."),
    file_process_type: str = Form(..., description="The type of file processing."),
    medical_record_status: str = Form(..., description="The status of the medical record."),
    uploaded_files: list[UploadFile] | None = File(default=None, description="List of uploaded files."),
    process_category: list[str] = Form(
        default=[],
        description="""
        Specifies the file names and their corresponding numeric categories with page ranges.
        Format: Each item in the list should be 'filename:category1=[pages],category2=[pages]'

        Scenarios and examples:
        1. Single file, single category, all pages:
           ['patient_record.pdf:1=[]']

        2. Single file, multiple categories, specific pages:
           ['patient_record.pdf:1=[1-3],2=[4-6]']

        3. Multiple files:
           ['patient_record.pdf:1=[1-2],2=[3-4]', 'consent_form.pdf:3=[]']

        4. Specific page numbers:
           ['patient_record.pdf:1=[1,3,5],2=[2,4,6]']

        5. Mixed ranges and specific pages:
           ['patient_record.pdf:1=[1-3,5,7-9],2=[4,6,10-12]']

        Notes:
        - Categories are represented by numbers (1, 2, 3, etc.).
        - Use empty brackets '[]' to include all pages for a category.
        - Page ranges are inclusive (e.g., [1-3] includes pages 1, 2, and 3).
        - Page numbers start from 1, not 0.
        - Each file mentioned in process_category must have a corresponding uploaded file or exist in the previous record.
        - Even for a single file, use a list with one item: ['filename:category=[pages]'].
        """
    ),
    created_by: str = Form(..., description="The user who created or updated the record."),
    session: Session = Depends(get_session),
):
    """
    Handles the creation or update of a job medical record.
    """
    logger.info("Received request to create/update job medical record")
    start_time = time.time()

    try:
        # Log input values
        logger.info(f"Request parameters - job_audit_id: {job_audit_id}, "
                   f"audit_transaction_id: {audit_transaction_id}, "
                   f"job_state: {job_state}, "
                   f"file_process_type: {file_process_type}, "
                   f"medical_record_status: {medical_record_status}, "
                   f"uploaded_files count: {len(uploaded_files) if uploaded_files else 0}, "
                   f"process_category: {process_category}, "
                   f"created_by: {created_by}")

        process_category = apply_category_workaround_for_swagger_bug(process_category)

        if not created_by:
            logger.error("User identifier not found in token")
            raise HTTPException(
                status_code=401, detail="User identifier not found in token"
            )

        result = JobMedicalRecordService.create_or_update_job_medical_record(
            session,
            job_audit_id,
            audit_transaction_id,
            job_state,
            file_process_type,
            medical_record_status,
            uploaded_files,
            process_category,
            created_by,
        )

        response_time = time.time() - start_time
        logger.info(f"Successfully created/updated job medical record. Response time: {response_time:.2f} seconds")
        logger.info(f"Response - job_medical_record_id: {result.id}, "
                   f"audit_transaction_id: {result.audit_transaction_id}, "
                   f"job_state: {result.job_state}, "
                   f"medical_record_status: {result.medical_record_status}")
        return result
    except Exception as e:
        logger.error(f"Error creating/updating job medical record: {str(e)}")
        raise


@app.get(
    "/job-medical-record/{job_medical_record_id:int}",
    response_model=SamJobMedicalRecordPublicWithRelated,
)
async def get_job_medical_record(
    job_medical_record_id: int,
    active_only: bool = True,
    session: Session = Depends(get_session),
):
    """
    Retrieves a job medical record by its ID.
    """
    logger.info(f"Received request to get job medical record. ID: {job_medical_record_id}, active_only: {active_only}")
    start_time = time.time()

    try:
        record = JobMedicalRecordService.get_job_medical_record(
            session, job_medical_record_id, active_only=active_only
        )
        if record is None:
            logger.warning(f"Job medical record not found. ID: {job_medical_record_id}")
            raise HTTPException(status_code=404, detail="Job medical record not found")
        
        response_time = time.time() - start_time
        logger.info(f"Successfully retrieved job medical record. Response time: {response_time:.2f} seconds")
        logger.info(f"Response - job_medical_record_id: {record.id}, "
                   f"audit_transaction_id: {record.audit_transaction_id}, "
                   f"job_state: {record.job_state}, "
                   f"medical_record_status: {record.medical_record_status}")
        return record
    except Exception as e:
        logger.error(f"Error retrieving job medical record: {str(e)}")
        raise


@app.get(
    "/job-medical-records", response_model=list[SamJobMedicalRecordPublicWithRelated]
)
async def list_job_medical_records(
    job_audit_id: int | None = None,
    audit_transaction_id: str | None = None,
    job_state: int | None = None,
    active_only: bool = True,
    session: Session = Depends(get_session),
):
    """
    Lists all job medical records, optionally filtered by job_audit_id and job_state.
    """
    logger.info(f"Received request to list job medical records. Filters - "
               f"job_audit_id: {job_audit_id}, "
               f"audit_transaction_id: {audit_transaction_id}, "
               f"job_state: {job_state}, "
               f"active_only: {active_only}")
    start_time = time.time()

    try:
        records = JobMedicalRecordService.list_job_medical_records(
            session, job_audit_id, audit_transaction_id, job_state, active_only
        )
        response_time = time.time() - start_time
        logger.info(f"Successfully listed job medical records. Count: {len(records)}. Response time: {response_time:.2f} seconds")
        # Log summary of each record
        for idx, record in enumerate(records):
            logger.info(f"Record {idx + 1}/{len(records)} - "
                       f"job_medical_record_id: {record.id}, "
                       f"audit_transaction_id: {record.audit_transaction_id}, "
                       f"job_state: {record.job_state}")
        return records
    except Exception as e:
        logger.error(f"Error listing job medical records: {str(e)}")
        raise


@app.get(
    "/sam-job-medical-record-process/{sam_job_medical_record_process_id:int}",
    response_model=SamJobMedicalRecordProcessPublic,
)
async def get_sam_job_medical_record_process(
    sam_job_medical_record_process_id: int,
    session: Session = Depends(get_session),
):
    logger.info(f"Received request to get medical record process. ID: {sam_job_medical_record_process_id}")
    start_time = time.time()

    try:
        record = JobMedicalRecordService.get_sam_job_medical_record_process(
            session, sam_job_medical_record_process_id
        )
        if record is None:
            logger.warning(f"Medical record process not found. ID: {sam_job_medical_record_process_id}")
            raise HTTPException(
                status_code=404, detail="Sam job medical record process not found"
            )
        
        response_time = time.time() - start_time
        logger.info(f"Successfully retrieved medical record process. Response time: {response_time:.2f} seconds")
        logger.info(f"Response - process_id: {record.id}, "
                   f"job_medical_record_id: {record.job_medical_record_id}, "
                   f"file_name: {record.file_name}, "
                   f"file_process_category: {record.file_process_category}")
        return record
    except Exception as e:
        logger.error(f"Error retrieving medical record process: {str(e)}")
        raise


@app.post("/auditor-document", response_model=list[SamMasterAuditorDocumentPublic])
async def create_auditor_document(
    master_auditor_id: int = Form(...),
    uploaded_files: list[UploadFile] = File(...),
    session: Session = Depends(get_session),
    created_by: str = Form(...),
):
    """
    Handles the upload of auditor documents.
    """
    logger.info(f"Received request to create auditor document. "
               f"master_auditor_id: {master_auditor_id}, "
               f"uploaded_files count: {len(uploaded_files)}, "
               f"created_by: {created_by}")
    start_time = time.time()

    try:
        result = AuditorDocumentService.create_auditor_documents(
            session,
            master_auditor_id,
            uploaded_files,
            created_by,
        )
        response_time = time.time() - start_time
        logger.info(f"Successfully created auditor documents. Count: {len(result)}. Response time: {response_time:.2f} seconds")
        # Log summary of created documents
        for idx, doc in enumerate(result):
            logger.info(f"Document {idx + 1}/{len(result)} - "
                       f"id: {doc.id}, "
                       f"file_name: {doc.file_name}, "
                       f"file_path: {doc.file_path}")
        return result
    except Exception as e:
        logger.error(f"Error creating auditor documents: {str(e)}")
        raise


@app.get(
    "/auditor-document/{document_id}", response_model=SamMasterAuditorDocumentPublic
)
async def get_auditor_document(
    document_id: int,
    session: Session = Depends(get_session),
):
    """
    Retrieves an auditor document by its ID.
    """
    logger.info(f"Received request to get auditor document. ID: {document_id}")
    start_time = time.time()

    try:
        record = AuditorDocumentService.get_auditor_document(session, document_id)
        if record is None:
            logger.warning(f"Auditor document not found. ID: {document_id}")
            raise HTTPException(status_code=404, detail="Auditor document not found")
        
        response_time = time.time() - start_time
        logger.info(f"Successfully retrieved auditor document. Response time: {response_time:.2f} seconds")
        logger.info(f"Response - document_id: {record.id}, "
                   f"file_name: {record.file_name}, "
                   f"file_path: {record.file_path}, "
                   f"created_by: {record.created_by}")
        return record
    except Exception as e:
        logger.error(f"Error retrieving auditor document: {str(e)}")
        raise


@app.get("/files/{file_path:path}")
async def get_file(file_path: str):
    """
    Retrieves a stored file by its path.

    Note: The file_path must be URL encoded before sending.
    """
    logger.info(f"Received request to get file. Path: {file_path}")
    start_time = time.time()

    try:
        decoded_path = unquote(file_path)
        if not path.exists(decoded_path):
            logger.warning(f"File not found. Path: {decoded_path}")
            raise HTTPException(status_code=404, detail="File not found")
        
        response_time = time.time() - start_time
        logger.info(f"Successfully located file. Response time: {response_time:.2f} seconds")
        logger.info(f"Serving file: {decoded_path}")
        return FileResponse(decoded_path)
    except Exception as e:
        logger.error(f"Error retrieving file: {str(e)}")
        raise


@app.post("/pa-kidney-upload", response_model=list[SamPaKidneyUploadPublic])
async def create_pa_kidney_upload(
    pa_kidney_id: int = Form(..., description="The ID of the PA Kidney record."),
    pa_kidney_lab_id: int | None = Form(None, description="The ID of the PA Kidney Lab record."),
    pa_kidney_ip_detail_id: int | None = Form(None, description="The ID of the PA Kidney IP Detail record."),
    pa_kidney_appeal_id: int | None = Form(None, description="The ID of the PA Kidney Appeal record."),
    upload_type: str = Form(..., description="The type of upload (e.g., 'results', 'documents')."),
    uploaded_files: list[UploadFile] = File(..., description="List of uploaded files."),
    created_by: str = Form(..., description="The user who is creating the upload."),
    session: Session = Depends(get_session),
):
    """
    Handles the upload of files for a PA Kidney record.
    """
    logger.info(f"Received request to create PA Kidney upload. "
               f"pa_kidney_id: {pa_kidney_id}, "
               f"pa_kidney_lab_id: {pa_kidney_lab_id}, "
               f"pa_kidney_ip_detail_id: {pa_kidney_ip_detail_id}, "
               f"pa_kidney_appeal_id: {pa_kidney_appeal_id}, "
               f"upload_type: {upload_type}, "
               f"uploaded_files count: {len(uploaded_files)}, "
               f"created_by: {created_by}")
    start_time = time.time()

    # Validate that only one of pa_kidney_lab_id or pa_kidney_ip_detail_id is provided
    if pa_kidney_lab_id is not None and pa_kidney_ip_detail_id is not None:
        raise HTTPException(
            status_code=400, 
            detail="Only one of pa_kidney_lab_id or pa_kidney_ip_detail_id can be provided, not both."
        )

    try:
        result = PaKidneyUploadService.create_kidney_uploads(
            session,
            pa_kidney_id,
            pa_kidney_lab_id,
            pa_kidney_ip_detail_id,
            pa_kidney_appeal_id,
            upload_type,
            uploaded_files,
            created_by,
        )
        response_time = time.time() - start_time
        logger.info(f"Successfully created PA Kidney uploads. Count: {len(result)}. Response time: {response_time:.2f} seconds")
        # Log summary of created documents
        for idx, upload in enumerate(result):
            logger.info(f"Upload {idx + 1}/{len(result)} - "
                       f"id: {upload.id}, "
                       f"file_name: {upload.file_name}, "
                       f"file_path: {upload.file_path}")
        return result
    except Exception as e:
        logger.error(f"Error creating PA Kidney uploads: {str(e)}")
        raise


@app.get(
    "/pa-kidney-upload/{upload_id}", response_model=SamPaKidneyUploadPublic
)
async def get_pa_kidney_upload(
    upload_id: int,
    session: Session = Depends(get_session),
):
    """
    Retrieves a PA Kidney upload by its ID.
    """
    logger.info(f"Received request to get PA Kidney upload. ID: {upload_id}")
    start_time = time.time()

    try:
        record = PaKidneyUploadService.get_kidney_upload(session, upload_id)
        if record is None:
            logger.warning(f"PA Kidney upload not found. ID: {upload_id}")
            raise HTTPException(status_code=404, detail="PA Kidney upload not found")
        
        response_time = time.time() - start_time
        logger.info(f"Successfully retrieved PA Kidney upload. Response time: {response_time:.2f} seconds")
        logger.info(f"Response - upload_id: {record.id}, "
                   f"pa_kidney_id: {record.pa_kidney_id}, "
                   f"pa_kidney_lab_id: {record.pa_kidney_lab_id}, "
                   f"pa_kidney_ip_detail_id: {record.pa_kidney_ip_detail_id}, "
                   f"pa_kidney_appeal_id: {record.pa_kidney_appeal_id}, "
                   f"file_name: {record.file_name}, "
                   f"file_path: {record.file_path}")
        return record
    except Exception as e:
        logger.error(f"Error retrieving PA Kidney upload: {str(e)}")
        raise


@app.get("/download-pa-kidney-files/{pa_kidney_id}")
async def download_pa_kidney_files(
    pa_kidney_id: str,
    session: Session = Depends(get_session),
):
    """
    Download all files for a specific PA_KIDNEY_ID as a zip file.
    
    This endpoint:
    1. Queries the SAM_PA_KIDNEY_UPLOAD table for files with the specified PA_KIDNEY_ID and STATUS = 'A'
    2. Creates a zip file containing all the found files
    3. Returns the zip file as a downloadable response
    
    Args:
        pa_kidney_id: The PA Kidney ID to download files for
        
    Returns:
        A zip file containing all the files for the specified PA_KIDNEY_ID
    """
    logger.info(f"Received request to download files for PA_KIDNEY_ID: {pa_kidney_id}")
    start_time = time.time()
    
    zip_path = None
    try:
        # Download files and create zip
        zip_path = FileDownloadService.download_pa_kidney_files(session, pa_kidney_id)
        
        # Get filename for response
        zip_filename = os.path.basename(zip_path)
        
        response_time = time.time() - start_time
        logger.info(f"Successfully created zip file for PA_KIDNEY_ID {pa_kidney_id}. "
                   f"Response time: {response_time:.2f} seconds. "
                   f"Zip file: {zip_filename}")
        
        # Return the zip file as a downloadable response
        return FileResponse(
            path=zip_path,
            filename=zip_filename,
            media_type="application/zip",
            headers={
                "Content-Disposition": f"attachment; filename={zip_filename}",
                "Cache-Control": "no-cache"
            }
        )
        
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Unexpected error downloading files for PA_KIDNEY_ID {pa_kidney_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")
    finally:
        # Clean up temporary files after a delay to ensure the file is served
        if zip_path:
            # Schedule cleanup after response is sent
            import asyncio
            async def cleanup_after_response():
                await asyncio.sleep(5)  # Wait 5 seconds to ensure file is served
                FileDownloadService.cleanup_temp_files(zip_path)
            
            # Start cleanup task
            asyncio.create_task(cleanup_after_response())

