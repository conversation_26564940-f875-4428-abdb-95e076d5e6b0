from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>, UploadFile
from sqlmodel import Session, select, update
from app.models import SamPaKidneyUpload
from app.services.datetime_service import get_current_bangkok_datetime
from app.services.pdf_service import save_original_files, get_base_path
import os
import logging

logger = logging.getLogger(__name__)

class PaKidneyUploadService:
    @staticmethod
    def create_kidney_uploads(
        session: Session,
        pa_kidney_id: int,
        pa_kidney_lab_id: int | None,
        pa_kidney_ip_detail_id: int | None,
        pa_kidney_appeal_id: int | None,
        upload_type: str,
        uploaded_files: list[UploadFile],
        created_by: str,
    ) -> list[SamPaKidneyUpload]:
        """
        Upload files for a PA Kidney record.
        
        Args:
            session: The database session
            pa_kidney_id: The ID of the PA Kidney record
            pa_kidney_lab_id: The ID of the PA Kidney Lab record
            pa_kidney_ip_detail_id: The ID of the PA Kidney IP Detail record
            pa_kidney_appeal_id: The ID of the PA Kidney Appeal record
            upload_type: The type of upload (e.g., 'results', 'documents')
            uploaded_files: List of uploaded files
            created_by: The user who is creating the upload
            
        Returns:
            A list of created upload records
        """
        try:
            # <PERSON> existing uploads of the same type as expired ('E')
            if pa_kidney_lab_id is not None:
                existing_stmt = select(SamPaKidneyUpload).where(
                    SamPaKidneyUpload.pa_kidney_id == pa_kidney_id, 
                    SamPaKidneyUpload.pa_kidney_lab_id == pa_kidney_lab_id,
                    SamPaKidneyUpload.type == upload_type,
                    SamPaKidneyUpload.status == "A"
                )
            elif pa_kidney_ip_detail_id is not None:
                existing_stmt = select(SamPaKidneyUpload).where(
                    SamPaKidneyUpload.pa_kidney_id == pa_kidney_id, 
                    SamPaKidneyUpload.pa_kidney_ip_detail_id == pa_kidney_ip_detail_id,
                    SamPaKidneyUpload.type == upload_type,
                    SamPaKidneyUpload.status == "A"
                )
            # Appeal upload type concat _appeal casuse nothing to check existing
            elif pa_kidney_appeal_id is not None:
                existing_stmt = select(SamPaKidneyUpload).where(
                    SamPaKidneyUpload.pa_kidney_id == pa_kidney_id, 
                    SamPaKidneyUpload.pa_kidney_appeal_id == pa_kidney_appeal_id,
                    SamPaKidneyUpload.type == upload_type + "_appeal",
                    SamPaKidneyUpload.status == "A"
                )
            else:
                existing_stmt = select(SamPaKidneyUpload).where(
                    (SamPaKidneyUpload.pa_kidney_id == pa_kidney_id),
                    SamPaKidneyUpload.type == upload_type,
                    SamPaKidneyUpload.status == "A"
                )
            existing_uploads = session.execute(existing_stmt).scalars().all()
            
            for existing_upload in existing_uploads:
                existing_upload.status = "E"
                existing_upload.updated_date = get_current_bangkok_datetime()
                existing_upload.updated_by = created_by
            
            uploads = []
            if pa_kidney_lab_id is not None:
                base_path = get_base_path(pa_kidney_lab_id, "kidney_lab", upload_type)
            elif pa_kidney_ip_detail_id is not None:
                base_path = get_base_path(pa_kidney_ip_detail_id, "kidney_ip_detail", upload_type)
            elif pa_kidney_appeal_id is not None:
                base_path = get_base_path(pa_kidney_appeal_id, "kidney_appeal", upload_type)
            else:
                base_path = get_base_path(pa_kidney_id, "kidney", upload_type)
            file_paths = save_original_files(uploaded_files, base_path)
            
            # Get the highest current sequence number for this kidney_id and type
            if pa_kidney_lab_id is not None:
                stmt = select(SamPaKidneyUpload.seq).where(
                    SamPaKidneyUpload.pa_kidney_id == pa_kidney_id, 
                    SamPaKidneyUpload.pa_kidney_lab_id == pa_kidney_lab_id,
                    SamPaKidneyUpload.type == upload_type
                ).order_by(SamPaKidneyUpload.seq.desc()).limit(1)
            elif pa_kidney_ip_detail_id is not None:
                stmt = select(SamPaKidneyUpload.seq).where(
                    SamPaKidneyUpload.pa_kidney_id == pa_kidney_id, 
                    SamPaKidneyUpload.pa_kidney_ip_detail_id == pa_kidney_ip_detail_id,
                    SamPaKidneyUpload.type == upload_type
                ).order_by(SamPaKidneyUpload.seq.desc()).limit(1)
            elif pa_kidney_appeal_id is not None:
                stmt = select(SamPaKidneyUpload.seq).where(
                    SamPaKidneyUpload.pa_kidney_id == pa_kidney_id, 
                    SamPaKidneyUpload.pa_kidney_appeal_id == pa_kidney_appeal_id,
                    SamPaKidneyUpload.type == upload_type
                ).order_by(SamPaKidneyUpload.seq.desc()).limit(1)
            else:
                stmt = select(SamPaKidneyUpload.seq).where(
                    SamPaKidneyUpload.pa_kidney_id == pa_kidney_id,
                    SamPaKidneyUpload.type == upload_type
                ).order_by(SamPaKidneyUpload.seq.desc()).limit(1)
            
            result = session.execute(stmt).first()
            next_seq = 1 if result is None else (result[0] + 1)
            
            for idx, file_path in enumerate(file_paths):
                file_name = os.path.basename(file_path)
                file_size = os.path.getsize(file_path)
                file_type = uploaded_files[idx].content_type

                upload_data = {
                    "pa_kidney_id": pa_kidney_id,
                    "pa_kidney_lab_id": pa_kidney_lab_id,
                    "pa_kidney_ip_detail_id": pa_kidney_ip_detail_id,
                    "pa_kidney_appeal_id": pa_kidney_appeal_id,
                    "seq": next_seq + idx,
                    "type": upload_type,
                    "file_name": file_name,
                    "file_path": file_path,
                    "file_size": file_size,
                    "file_type": file_type,
                    "status": "A",
                    "created_date": get_current_bangkok_datetime(),
                    "created_by": created_by,
                }

                upload = SamPaKidneyUpload.create(
                    session, commit=False, **upload_data
                )
                uploads.append(upload)

            session.commit()
            # Refresh uploads from the database after commit
            uploads = [SamPaKidneyUpload.read(session, upload.id) for upload in uploads]
            uploads = [upload for upload in uploads if upload is not None]
            return uploads

        except Exception as e:
            session.rollback()
            logger.error(f"Error in create_kidney_uploads: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"An error occurred while creating kidney uploads: {str(e)}",
            )
    
    @staticmethod
    def get_kidney_upload(
        session: Session,
        upload_id: int,
    ) -> SamPaKidneyUpload | None:
        """
        Get a kidney upload by ID.
        
        Args:
            session: The database session
            upload_id: The ID of the upload
            
        Returns:
            The upload record or None if not found
        """
        return SamPaKidneyUpload.read(session, upload_id)
    
    @staticmethod
    def list_kidney_uploads(
        session: Session,
        pa_kidney_id: int,
        pa_kidney_lab_id: int | None = None,
        pa_kidney_ip_detail_id: int | None = None,
        pa_kidney_appeal_id: int | None = None,
        upload_type: str | None = None,
        active_only: bool = True,
    ) -> list[SamPaKidneyUpload]:
        """
        List all kidney uploads for a kidney ID, optionally filtered by type.
        
        Args:
            session: The database session
            pa_kidney_id: The ID of the PA Kidney record
            pa_kidney_lab_id: The ID of the PA Kidney Lab record
            pa_kidney_ip_detail_id: The ID of the PA Kidney IP Detail record
            pa_kidney_appeal_id: The ID of the PA Kidney Appeal record
            upload_type: Optional type to filter by
            active_only: Whether to return only active records
            
        Returns:
            List of upload records
        """
        if pa_kidney_lab_id is not None:
            query = select(SamPaKidneyUpload).where(
                SamPaKidneyUpload.pa_kidney_id == pa_kidney_id, 
                SamPaKidneyUpload.pa_kidney_lab_id == pa_kidney_lab_id
            )
        elif pa_kidney_ip_detail_id is not None:
            query = select(SamPaKidneyUpload).where(
                SamPaKidneyUpload.pa_kidney_id == pa_kidney_id, 
                SamPaKidneyUpload.pa_kidney_ip_detail_id == pa_kidney_ip_detail_id
            )
        elif pa_kidney_appeal_id is not None:
            query = select(SamPaKidneyUpload).where(
                SamPaKidneyUpload.pa_kidney_id == pa_kidney_id, 
                SamPaKidneyUpload.pa_kidney_appeal_id == pa_kidney_appeal_id
            )
        else:
            query = select(SamPaKidneyUpload).where(
                SamPaKidneyUpload.pa_kidney_id == pa_kidney_id
            )
        
        if upload_type:
            query = query.where(SamPaKidneyUpload.type == upload_type)
            
        if active_only:
            query = query.where(SamPaKidneyUpload.status == "A")
            
        query = query.order_by(SamPaKidneyUpload.seq)
        return session.execute(query).scalars().all()
    
    @staticmethod
    def delete_kidney_upload(
        session: Session,
        upload_id: int,
        updated_by: str,
    ) -> SamPaKidneyUpload | None:
        """
        Mark a kidney upload as deleted.
        
        Args:
            session: The database session
            upload_id: The ID of the upload to delete
            updated_by: The user who is deleting the upload
            
        Returns:
            The updated upload record or None if not found
        """
        try:
            upload = SamPaKidneyUpload.delete(session, updated_by, upload_id)
            return upload
        except Exception as e:
            session.rollback()
            raise HTTPException(
                status_code=500,
                detail=f"An error occurred while deleting kidney upload: {str(e)}",
            ) 